{"type": "module", "private": true, "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "build:example": "vite build --mode example", "build:nt": "vite build --mode nt", "serve:example": "http-server ./dist-example -o", "svgo": "svgo -f src/assets/icons", "new": "plop", "generate:icons": "tsx ./scripts/generate.icons.ts", "lint": "npm-run-all -s lint:tsc lint:eslint lint:stylelint", "lint:tsc": "vue-tsc -b", "lint:eslint": "eslint . --cache --fix", "lint:stylelint": "stylelint \"src/**/*.{css,scss,vue}\" --cache --fix", "preinstall": "npx only-allow pnpm", "postinstall": "simple-git-hooks", "taze": "taze minor -wIr"}, "dependencies": {"@antv/g2plot": "^2.4.33", "@arcgis/core": "^4.33.12", "@bytemd/plugin-gfm": "^1.22.0", "@bytemd/vue-next": "^1.22.0", "@fortawesome/fontawesome-free": "^7.0.0", "@mertdeveci55/univer-import-export": "0.1.51", "@number-flow/vue": "^0.4.8", "@terraformer/wkt": "^2.2.1", "@tinymce/tinymce-vue": "^6.2.0", "@turf/turf": "^7.2.0", "@types/crypto-js": "^4.2.2", "@univerjs/core": "^0.10.9", "@univerjs/design": "^0.10.9", "@univerjs/preset-sheets-advanced": "^0.10.9", "@univerjs/preset-sheets-core": "^0.10.9", "@univerjs/preset-sheets-data-validation": "^0.10.9", "@univerjs/preset-sheets-drawing": "^0.10.9", "@univerjs/preset-sheets-hyper-link": "^0.10.9", "@univerjs/presets": "^0.10.9", "@univerjs/sheets": "^0.10.9", "@univerjs/sheets-ui": "^0.10.9", "@univerjs/ui": "^0.10.9", "@univerjs/ui-adapter-vue3": "^0.10.9", "@vee-validate/zod": "^4.15.1", "@visactor/vchart": "^2.0.0", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vue-office/pptx": "^1.0.1", "@vueuse/components": "^13.4.0", "@vueuse/core": "^13.4.0", "@vueuse/integrations": "^13.4.0", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "axios": "^1.10.0", "bytemd": "^1.22.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "defu": "^6.1.4", "disable-devtool": "^0.3.8", "echarts": "^5.6.0", "element-plus": "^2.10.2", "embla-carousel-auto-scroll": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-fade": "^8.6.0", "embla-carousel-vue": "^8.6.0", "eruda": "^3.4.3", "es-toolkit": "^1.39.5", "esri-loader": "^3.7.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "hotkeys-js": "^3.13.14", "leaflet": "^1.9.4", "lucide-vue-next": "^0.525.0", "luckyexcel": "^1.0.1", "medium-zoom": "^1.1.0", "mitt": "^3.0.1", "motion-v": "^1.3.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^3.0.3", "pinyin-pro": "^3.26.0", "print-js": "^1.6.0", "qrcode": "^1.5.4", "qs": "^6.14.0", "read-excel-file": "^5.8.8", "reka-ui": "^2.3.1", "scule": "^1.3.0", "sortablejs": "^1.15.6", "spinkit": "^2.0.1", "splitpanes": "^4.0.4", "swiper": "^11.2.10", "tailwind-merge": "^3.3.1", "tinymce": "^7.9.1", "ua-parser-js": "^2.0.4", "v-wave": "^3.0.2", "vconsole": "^3.15.1", "vee-validate": "^4.15.1", "vue": "3.5.17", "vue-currency-input": "^3.2.1", "vue-data-ui": "^2.12.6", "vue-demi": "0.14.10", "vue-esign": "^1.1.4", "vue-hooks-plus": "^2.4.0", "vue-i18n": "^11.1.7", "vue-router": "^4.5.1", "vue-sonner": "^2.0.1", "vue-tianditu": "^2.7.6", "vxe-table": "^4.13.49", "watermark-js-plus": "^1.6.2", "xe-utils": "^3.7.5", "xlsx": "^0.18.5", "zod": "^3.25.67"}, "devDependencies": {"@antfu/eslint-config": "^4.16.1", "@clack/prompts": "^0.11.0", "@faker-js/faker": "^9.8.0", "@iconify/json": "^2.2.353", "@iconify/vue": "^5.0.0", "@intlify/eslint-plugin-vue-i18n": "^4.0.1", "@intlify/unplugin-vue-i18n": "^6.0.8", "@stylistic/stylelint-config": "^2.0.0", "@types/canvas-confetti": "^1.9.0", "@types/file-saver": "^2.0.7", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.14.0", "@types/sortablejs": "^1.15.8", "@types/splitpanes": "^2.2.6", "@unocss/eslint-plugin": "^66.3.2", "@unocss/preset-legacy-compat": "^66.3.2", "@vitejs/plugin-legacy": "^7.0.0", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "boxen": "^8.0.1", "eslint": "^9.30.0", "fs-extra": "^11.3.0", "http-server": "^14.1.1", "lint-staged": "^16.1.2", "npm-run-all2": "^8.0.4", "picocolors": "^1.1.1", "plop": "^4.0.1", "postcss": "^8.5.6", "postcss-nested": "^7.0.2", "sass-embedded": "^1.89.2", "simple-git-hooks": "^2.13.0", "stylelint": "^16.21.0", "stylelint-config-recess-order": "^7.1.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-config-standard-vue": "^1.0.0", "stylelint-scss": "^6.12.1", "svgo": "^4.0.0", "taze": "^19.1.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "unocss": "^66.3.2", "unocss-preset-animations": "^1.2.1", "unplugin-auto-import": "^19.3.0", "unplugin-turbo-console": "^2.1.3", "unplugin-vue-components": "^28.8.0", "vite": "^7.1.5", "vite-plugin-app-loading": "^0.4.0", "vite-plugin-archiver": "^0.2.0", "vite-plugin-banner": "^0.8.1", "vite-plugin-compression2": "^2.2.0", "vite-plugin-env-parse": "^1.0.15", "vite-plugin-fake-server": "^2.2.0", "vite-plugin-pages": "^0.33.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.7", "vite-plugin-vue-meta-layouts": "^0.5.1", "vue-tsc": "^2.2.10"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged", "preserveUnused": true}}